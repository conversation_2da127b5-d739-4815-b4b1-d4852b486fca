@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-white;
  }
}

/* Estilos globais para o tema dark */
:root {
  --bs-body-bg: #1a1a2e;
  --bs-body-color: #ffffff;
}

body {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  color: #ffffff;
}

/* Melhorias para inputs no dark mode */
.form-control, .form-select {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

.form-control:focus, .form-select:focus {
  background-color: rgba(255, 255, 255, 0.15) !important;
  border-color: #0d6efd !important;
  color: #ffffff !important;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* <PERSON>hor<PERSON> para cards */
.card {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Melhorias para tabelas */
.table {
  color: #ffffff !important;
}

.table-dark {
  background-color: rgba(0, 0, 0, 0.3) !important;
}

.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* Melhorias para paginação */
.page-link {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

.page-link:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

.page-item.disabled .page-link {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.4) !important;
}

/* Scrollbar customizada */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Animações suaves */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
} 