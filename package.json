{"name": "trade_nb_members", "version": "1.0.0", "main": "index.js", "scripts": {"test": "jest", "dev": "ts-node src/index.ts", "build": "tsc", "api": "ts-node src/api/index.ts", "frontend:dev": "vite", "frontend:build": "vite build", "dev:all": "concurrently \"npm run api\" \"npm run frontend:dev\" \"npm run dev\"", "build:all": "npm run frontend:build && NODE_ENV=production npm run api"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.2", "@types/winston": "^2.4.4", "@types/ws": "^8.18.1", "@vitejs/plugin-vue": "^5.2.4", "axios": "^1.9.0", "bootstrap": "^5.3.6", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.66.0", "play-sound": "^1.1.6", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^6.3.5", "vue": "^3.5.14", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1", "winston": "^3.17.0", "ws": "^8.18.2"}, "devDependencies": {"@popperjs/core": "^2.11.8", "@types/bootstrap": "^5.2.10", "@types/jest": "^29.5.14", "@types/node-cron": "^3.0.11", "@types/node-telegram-bot-api": "^0.64.9", "@types/play-sound": "^1.1.2", "@types/sqlite3": "^3.1.11", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "jest": "^29.7.0", "ts-jest": "^29.3.2"}}