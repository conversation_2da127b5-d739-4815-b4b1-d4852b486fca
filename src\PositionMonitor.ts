import { PositionValidator } from './PositionValidator';
import { BingXWebSocket } from './BingXWebSocket';
import { TradeDatabase } from './TradeDatabase';
import { Position, MonitoredPosition } from './utils/types';
import { OrderMonitor, Order } from './OrderMonitor';
import { BingXOrderExecutor } from './BingXOrderExecutor';
import { normalizeSymbolBingX } from './utils/bingxUtils';
import { NotificationService } from './NotificationService';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export class PositionMonitor {
    private positionValidator: PositionValidator;
    private tradeDatabase: TradeDatabase;
    private orderMonitor: OrderMonitor;
    private orderExecutor: BingXOrderExecutor;
    private notificationService: NotificationService;
    private monitoredPositions: Map<string, MonitoredPosition> = new Map();
    private onPriceUpdate?: (position: MonitoredPosition) => void;
    private readonly limitOrderFee: number;
    private readonly marketOrderFee: number;

    constructor(onPriceUpdate?: (position: MonitoredPosition) => void) {
        this.positionValidator = new PositionValidator();
        this.tradeDatabase = new TradeDatabase();
        this.orderMonitor = new OrderMonitor();
        this.orderExecutor = new BingXOrderExecutor();
        this.notificationService = new NotificationService();
        this.onPriceUpdate = onPriceUpdate;
        this.limitOrderFee = parseFloat(process.env.BINGX_LIMIT_ORDER_FEE || '0.02');
        this.marketOrderFee = parseFloat(process.env.BINGX_MARKET_ORDER_FEE || '0.05');
    }

    private getPositionKey(symbol: string, positionSide: 'LONG' | 'SHORT'): string {
        const normalizedSymbol = normalizeSymbolBingX(symbol);
        return `${normalizedSymbol}_${positionSide}`;
    }

    private async matchPositionWithTrade(position: Position): Promise<number | undefined> {
        const normalizedSymbol = normalizeSymbolBingX(position.symbol);
        const trades = await this.tradeDatabase.getOpenTrades();
        const matchingTrades = trades.filter(trade => 
            normalizeSymbolBingX(trade.symbol) === normalizedSymbol && 
            trade.type === position.positionSide
        );
        
        if (matchingTrades.length === 0) {
            return undefined;
        }
        
        // Return the trade with the maximum ID
        return Math.max(...matchingTrades.map(trade => trade.id));
    }

    private async checkStopLossBeforeLiquidation(
        position: Position,
        stopLossOrder: Order | undefined
    ): Promise<void> {
        if (!stopLossOrder || parseFloat(position.positionAmt) === 0) {
            return;
        }

        const stopPrice = parseFloat(stopLossOrder.stopPrice);
        const currentPrice = parseFloat(position.markPrice);
        const entryPrice = parseFloat(position.avgPrice);
        
        // Calculate liquidation price based on leverage and position side
        const liquidationPrice = parseFloat(position.liquidationPrice); // For SHORT positions

        const isBeforeLiquidation = position.positionSide === 'LONG'
            ? stopPrice < liquidationPrice
            : stopPrice > liquidationPrice;

        if (isBeforeLiquidation) {
            await this.notificationService.sendTradeNotification({
                symbol: position.symbol,
                type: position.positionSide,
                entry: entryPrice,
                stop: stopPrice,
                takeProfits: {
                    tp1: 0,
                    tp2: null,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: true,
                    message: `Stop loss order (${stopPrice}) found before liquidation price (${liquidationPrice}) for ${position.symbol} ${position.positionSide}`,
                    volumeAnalysis: {
                        color: 'red',
                        stdBar: 0,
                        currentVolume: 0,
                        mean: 0,
                        std: 0
                    },
                    entryAnalysis: {
                        currentClose: currentPrice,
                        canEnter: false,
                        hasClosePriceBeforeEntry: true,
                        message: 'Stop loss before liquidation - DANGER'
                    }
                },
                analysisUrl: '',
                volume_required: false,
                volume_adds_margin: false,
                setup_description: `⚠️ DANGER: Stop loss order (${stopPrice}) is before liquidation price (${liquidationPrice}) for ${position.symbol} ${position.positionSide} position. `,
                interval: null
            });
        }
    }

    private async createStopLossOrder(
        position: Position,
        initialStopPrice: number
    ): Promise<Order | undefined> {
        if (!initialStopPrice || parseFloat(position.positionAmt) === 0) {
            return undefined;
        }

        try {
            const newStopOrder = await this.orderExecutor.placeOrder(
                position.symbol,
                position.positionSide === 'LONG' ? 'SELL' : 'BUY',
                position.positionSide,
                'STOP',
                initialStopPrice,
                initialStopPrice,
                parseFloat(position.positionAmt)
            );

            const stopLossOrder: Order = {
                orderId: newStopOrder.data.order.orderId,
                symbol: position.symbol,
                side: position.positionSide === 'LONG' ? 'SELL' : 'BUY',
                type: 'STOP',
                price: initialStopPrice.toString(),
                stopPrice: initialStopPrice.toString(),
                quantity: position.positionAmt,
                positionSide: position.positionSide,
                status: 'NEW',
                clientOrderId: newStopOrder.data.order.clientOrderId || '',
                createTime: Date.now(),
                updateTime: Date.now()
            };

            // Send notification about the new stop loss order
            await this.notificationService.sendTradeNotification({
                symbol: position.symbol,
                type: position.positionSide,
                entry: parseFloat(position.avgPrice),
                stop: initialStopPrice,
                takeProfits: {
                    tp1: 0,
                    tp2: null,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: true,
                    message: `New stop loss order created for ${position.symbol} ${position.positionSide}`,
                    volumeAnalysis: {
                        color: 'green',
                        stdBar: 0,
                        currentVolume: 0,
                        mean: 0,
                        std: 0
                    },
                    entryAnalysis: {
                        currentClose: parseFloat(position.markPrice),
                        canEnter: false,
                        hasClosePriceBeforeEntry: true,
                        message: 'Stop loss order created'
                    }
                },
                analysisUrl: '',
                volume_required: false,
                volume_adds_margin: false,
                setup_description: `✅ New stop loss order created for ${position.symbol} ${position.positionSide} position at ${initialStopPrice}. Entry: ${position.avgPrice}, Current Price: ${position.markPrice}`,
                interval: null
            });

            return stopLossOrder;
        } catch (error) {
            console.error(`Error creating stop loss order for ${position.symbol} ${position.positionSide}:`, error);
            
            // Send notification about the error
            await this.notificationService.sendTradeNotification({
                symbol: position.symbol,
                type: position.positionSide,
                entry: parseFloat(position.avgPrice),
                stop: initialStopPrice,
                takeProfits: {
                    tp1: 0,
                    tp2: null,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: false,
                    message: `Failed to create stop loss order for ${position.symbol} ${position.positionSide}`,
                    volumeAnalysis: {
                        color: 'red',
                        stdBar: 0,
                        currentVolume: 0,
                        mean: 0,
                        std: 0
                    },
                    entryAnalysis: {
                        currentClose: parseFloat(position.markPrice),
                        canEnter: false,
                        hasClosePriceBeforeEntry: true,
                        message: 'Error creating stop loss'
                    }
                },
                analysisUrl: '',
                volume_required: false,
                volume_adds_margin: false,
                setup_description: `❌ Failed to create stop loss order for ${position.symbol} ${position.positionSide} position at ${initialStopPrice}. Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                interval: null
            });

            return undefined;
        }
    }

    private async updateMonitoredPosition(position: Position): Promise<void> {
        const positionKey = this.getPositionKey(position.symbol, position.positionSide);
        const existingPosition = this.monitoredPositions.get(positionKey);

        // Update open orders to get latest stop loss orders
        await this.orderMonitor.updateOpenOrders();
        let stopLossOrder = this.orderMonitor.getStopMarketOrder(position.symbol, position.positionSide);

        // Get trade info to get leverage and initial stop price
        const tradeId = await this.matchPositionWithTrade(position);
        let leverage: number | undefined;
        leverage = parseFloat(position.leverage.toString());

        let initialStopPrice: number | undefined;
        if (tradeId) {
            const trade = await this.tradeDatabase.getTradeById(tradeId);
            initialStopPrice = trade?.stop; // Using the 'stop' field from TradeRecord

            // Check for stop loss before liquidation
            await this.checkStopLossBeforeLiquidation(position, stopLossOrder);

            // If no stop loss order exists but we have a trade stop price, create one
            if (!stopLossOrder && initialStopPrice) {
                stopLossOrder = await this.createStopLossOrder(position, initialStopPrice);
            }
        }

        if (existingPosition) {
            // Update existing position
            existingPosition.position = position;
            existingPosition.stopLossOrder = stopLossOrder;
            existingPosition.leverage = leverage;
            existingPosition.entryPrice = parseFloat(position.avgPrice);
            if (stopLossOrder) {
                existingPosition.initialStopPrice = parseFloat(stopLossOrder.stopPrice);
            }

        }

        if (!existingPosition?.websocket){
            // Create new monitored position
            const websocket = this.createWebSocket(position.symbol, position.positionSide);

            this.monitoredPositions.set(positionKey, {
                symbol: position.symbol,
                positionSide: position.positionSide,
                position,
                websocket,
                tradeId,
                lastPrice: undefined,
                stopLossOrder,
                initialStopPrice: stopLossOrder ? parseFloat(stopLossOrder.stopPrice) : undefined,
                entryPrice: parseFloat(position.avgPrice),
                leverage
            });
        }
    }

    private async checkAndUpdateStopLoss(position: MonitoredPosition, currentPrice: number): Promise<void> {
        if (!position.initialStopPrice || !position.entryPrice || !position.stopLossOrder || !position.leverage) {
            return;
        }

        const entryPrice = position.entryPrice;
        const currentStopPrice = parseFloat(position.stopLossOrder.stopPrice);
        const positionSide = position.positionSide;

        // Check if stop loss is already in profit
        const isStopLossInProfit = positionSide === 'LONG' 
            ? currentStopPrice >= entryPrice 
            : currentStopPrice <= entryPrice;
        
        if (isStopLossInProfit) {
            return; // Don't update if stop loss is already in profit
        }

        // Calculate risk (distance from entry to initial stop)
        const risk = Math.abs(entryPrice - currentStopPrice);
        
        // Calculate reward (distance from entry to current price)
        const reward = Math.abs(currentPrice - entryPrice);

        // Check if we've reached 1:1 risk/reward
        if (reward >= risk) {
            // Calculate position margin and total position value
            const positionAmt = Math.abs(parseFloat(position.position.positionAmt));
            const positionValue = positionAmt * entryPrice;
            const margin = positionValue / position.leverage;

            // Calculate fees based on total position value
            // Entry fee (market order) is applied to the total position value
            const entryFeeAmount = positionValue * (this.marketOrderFee / 100);
            // Exit fee (limit order) is applied to the total position value
            const exitFeeAmount = positionValue * (this.limitOrderFee / 100);
            const totalFeeAmount = entryFeeAmount + exitFeeAmount;

            // Convert fee amounts to price impact
            const feePriceImpact = totalFeeAmount / positionAmt;

            // Calculate breakeven price including fees
            const breakevenWithFees = positionSide === 'LONG' 
                ? entryPrice + feePriceImpact // For LONG: entry + fee impact
                : entryPrice - feePriceImpact; // For SHORT: entry - fee impact

            // Check if current price is better than breakeven price
            const isCurrentPriceBetter = positionSide === 'LONG'
                ? currentPrice > breakevenWithFees
                : currentPrice < breakevenWithFees;

            // Only update if current price is better than breakeven and current stop is not already at or better than breakeven + fees
            const shouldUpdate = isCurrentPriceBetter && (positionSide === 'LONG'
                ? currentStopPrice < breakevenWithFees
                : currentStopPrice > breakevenWithFees);

            if (shouldUpdate) {
                try {
                    // Place new stop loss order at breakeven + fees
                    const newStopOrder = await this.orderExecutor.cancelReplaceOrder(
                        position.symbol,
                        positionSide === 'LONG' ? 'SELL' : 'BUY',
                        positionSide,
                        'STOP',
                        breakevenWithFees, 
                        breakevenWithFees,
                        parseFloat(position.position.positionAmt),
                        position.tradeId,
                        position.stopLossOrder.orderId
                    );

                    // Update the monitored position with new stop loss order
                    position.stopLossOrder = {
                        ...position.stopLossOrder,
                        stopPrice: breakevenWithFees.toString()
                    };
                    
                    // Send notification about stop loss moved to breakeven
                    await this.notificationService.sendTradeNotification({
                        symbol: position.symbol,
                        type: positionSide,
                        entry: entryPrice,
                        stop: breakevenWithFees,
                        takeProfits: {
                            tp1: 0,
                            tp2: null,
                            tp3: null,
                            tp4: null,
                            tp5: null,
                            tp6: null
                        },
                        validation: {
                            isValid: true,
                            message: `Stop loss moved to breakeven + fees (${((totalFeeAmount / positionValue) * 100).toFixed(4)}% of position value)`,
                            volumeAnalysis: {
                                color: 'green',
                                stdBar: 0,
                                currentVolume: 0,
                                mean: 0,
                                std: 0
                            },
                            entryAnalysis: {
                                currentClose: currentPrice,
                                canEnter: false,
                                hasClosePriceBeforeEntry: true,
                                message: 'Stop loss moved to breakeven'
                            }
                        },
                        analysisUrl: '',
                        volume_required: false,
                        volume_adds_margin: false,
                        setup_description: `Stop loss moved to breakeven for ${position.symbol} ${positionSide} position. Fees: ${((totalFeeAmount / positionValue) * 100).toFixed(4)}% of position value (${totalFeeAmount.toFixed(8)}). Risk/Reward: ${(reward/risk).toFixed(2)}:1`,
                        interval: '1h'
                    });

                } catch (error) {
                    console.error(`Error updating stop loss for ${position.symbol} ${positionSide}:`, error);
                }
            }
        }
    }

    private createWebSocket(symbol: string, positionSide: 'LONG' | 'SHORT'): BingXWebSocket {
        const normalizedSymbol = normalizeSymbolBingX(symbol);
        const ws = new BingXWebSocket(normalizedSymbol, async (priceData) => {
            const positionKey = this.getPositionKey(normalizedSymbol, positionSide);
            const monitoredPosition = this.monitoredPositions.get(positionKey);
            
            if (monitoredPosition) {
                monitoredPosition.lastPrice = priceData.price;
                
                // Check and update stop loss if needed
                await this.checkAndUpdateStopLoss(monitoredPosition, priceData.price);
                
                if (this.onPriceUpdate) {
                    this.onPriceUpdate(monitoredPosition);
                }
            }
        });
        
        ws.connect();
        return ws;
    }

    private removeClosedPositions(currentPositions: Position[]): void {
        const currentPositionKeys = new Set(
            currentPositions.map(p => this.getPositionKey(p.symbol, p.positionSide))
        );

        // Remove positions that are no longer open
        for (const [positionKey, monitoredPosition] of this.monitoredPositions.entries()) {
            if (!currentPositionKeys.has(positionKey)) {
                monitoredPosition.websocket.disconnect();
                this.monitoredPositions.delete(positionKey);
            }
        }
    }

    public async updatePositions(): Promise<void> {
        try {
            let positions:Position[] = [];
            try{
                // Get all open positions from BingX
                positions = await this.positionValidator.getPositions('ALL');
            }catch (error) {
                console.error('Error updating positions:', error);
            }

            // Update open orders first
            await this.orderMonitor.updateOpenOrders();
            
            // Remove positions that are no longer open
            this.removeClosedPositions(positions);

            // Update or add new positions
            for (const position of positions) {
                if (parseFloat(position.positionAmt) !== 0) {
                    await this.updateMonitoredPosition(position);
                }
            }

            await this.orderMonitor.cancelOrphanedOrders(this.monitoredPositions);

        } catch (error) {
            console.error('Error updating positions:', error);
            throw error;
        }
    }

    public getMonitoredPositions(): MonitoredPosition[] {
        return Array.from(this.monitoredPositions.values());
    }

    public getMonitoredPosition(symbol: string, positionSide: 'LONG' | 'SHORT'): MonitoredPosition | undefined {
        return this.monitoredPositions.get(this.getPositionKey(symbol, positionSide));
    }

    public getMonitoredPositionsMap(): Map<string, MonitoredPosition> {
        return new Map(this.monitoredPositions);
    }

    public disconnect(): void {
        for (const monitoredPosition of this.monitoredPositions.values()) {
            monitoredPosition.websocket.disconnect();
        }
        this.monitoredPositions.clear();
    }
} 