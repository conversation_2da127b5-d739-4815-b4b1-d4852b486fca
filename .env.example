# BingX API Configuration
# Replace the values below with your actual API credentials
#BINGX_API_KEY=your_api_key_here
#BINGX_API_SECRET=your_api_secret_here
BINGX_BASE_URL=https://open-api-vst.bingx.com
BINGX_WS_URL=wss://open-api-swap.bingx.com/swap-market
BINGX_MARGIN=500
BINGX_LIMIT_ORDER_FEE=0.02
BINGX_MARKET_ORDER_FEE=0.05
VOLUME_MARGIN_PERCENTAGE=10
MAX_LEVERAGE=200
LEVERAGE_SAFETY_FACTOR_PERCENT=50
LOG_TO_CONSOLE=false
#If modify_tp1 is true, adjust tp1 to create 1:1 risk-reward ratio
MODIFY_TP1=false 

#TELEGRAM_BOT_TOKEN=your_bot_token_here
#TELEGRAM_CHAT_ID=your_chat_id_here
#Vou te explicar passo a passo como criar um bot no Telegram e obter o token e chat ID:
#Criar um Bot e obter o Token:
#Abra o Telegram e procure por "@BotFather"
#Inicie uma conversa com o BotFather
#Envie o comando /newbot
#Digite um nome para seu bot (ex: "Trade Notifications Bot")
#Digite um username para seu bot (deve terminar em 'bot', ex: "trade_notifications_bot")
#O BotFather irá te enviar uma mensagem com o token do seu bot, algo como:
#Apply to TelegramServ...
#Guarde este token - ele será seu TELEGRAM_BOT_TOKEN
#Obter o Chat ID:
#Existem algumas maneiras de obter o Chat ID:
#Método 1 - Usando o @userinfobot:
#Procure por "@userinfobot" no Telegram
#Inicie uma conversa com ele
#Envie qualquer mensagem
#O bot responderá com suas informações, incluindo seu Chat ID