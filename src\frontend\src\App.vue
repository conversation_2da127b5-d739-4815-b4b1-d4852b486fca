<template>
  <div class="min-vh-100">
    <nav class="navbar navbar-expand-lg bg-body-tertiary shadow-sm">
      <div class="container">
        <div class="d-flex justify-content-between w-100 align-items-center">
          <div class="d-flex align-items-center">
            <h1 class="h4 mb-0 me-4">Trade Manager</h1>
            <div class="navbar-nav">
              <router-link to="/" class="nav-link" active-class="active">Trades</router-link>
              <router-link to="/dashboard" class="nav-link" active-class="active">Dashboard</router-link>
              <router-link to="/notifications" class="nav-link" active-class="active">Notificações</router-link>
            </div>
          </div>
          <div class="d-flex align-items-center">
            <span class="badge" :class="connectionStatusClass">{{ connectionStatus }}</span>
          </div>
        </div>
      </div>
    </nav>


    <router-view v-slot="{ Component }">
      <component :is="Component" />
    </router-view>

    
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, provide } from 'vue'
import { TradeNotification } from '../../utils/types'

// WebSocket state
const connectionStatus = ref('Connecting...')
const connectionStatusClass = ref('bg-warning')
const trades = ref<TradeNotification[]>([])
let ws: WebSocket | null = null

const connectWebSocket = () => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const wsUrl = `${protocol}//localhost:3000`

  ws = new WebSocket(wsUrl)

  ws.onopen = () => {
    connectionStatus.value = 'Connected'
    connectionStatusClass.value = 'bg-success'
  }

  ws.onclose = () => {
    connectionStatus.value = 'Disconnected'
    connectionStatusClass.value = 'bg-danger'
    // Try to reconnect after 30 seconds
    setTimeout(connectWebSocket, 30000)
  }

  ws.onerror = (error) => {
    console.error('WebSocket error:', error)
    connectionStatus.value = 'Error'
    connectionStatusClass.value = 'bg-danger'
  }

  ws.onmessage = (event) => {
    try {
      console.log('Received trade notification:', event.data)
      const trade: TradeNotification = JSON.parse(event.data)
      trades.value.unshift(trade) // Add new trade at the beginning

      // Keep only the last 50 trades
      if (trades.value.length > 50) {
        trades.value = trades.value.slice(0, 50)
      }
    } catch (error) {
      console.error('Error parsing trade notification:', error)
    }
  }
}

// Provide trades data to child components
provide('trades', trades)

// Initialize dark mode and WebSocket
onMounted(() => {
  document.documentElement.setAttribute('data-bs-theme', 'dark')
  connectWebSocket()
})

onUnmounted(() => {
  if (ws) {
    ws.close()
  }
})
</script>

<style>
/* Add any custom styles here */
[data-bs-theme="dark"] {
  --bs-body-bg: #212529;
  --bs-body-color: #f8f9fa;
}

[data-bs-theme="dark"] .navbar {
  background-color: #343a40 !important;
}

[data-bs-theme="dark"] .card {
  background-color: #343a40;
  border-color: #495057;
}

[data-bs-theme="dark"] .table {
  color: #f8f9fa;
}

[data-bs-theme="dark"] .table-light {
  background-color: #495057;
  color: #f8f9fa;
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
  background-color: #495057;
  color: #f8f9fa;
}
</style> 