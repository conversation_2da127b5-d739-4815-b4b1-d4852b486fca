import { TelegramService } from '../src/TelegramService';
import { NotificationService } from '../src/NotificationService';
import { TradeNotification } from '../src/utils/types';
import dotenv from 'dotenv';

// Carrega variáveis de ambiente
dotenv.config();

// Mock do TradeDatabase para evitar problemas de inicialização
jest.mock('../src/TradeDatabase', () => {
    return {
        TradeDatabase: jest.fn().mockImplementation(() => ({
            saveTradeNotification: jest.fn().mockResolvedValue(undefined)
        }))
    };
});

// Mock do axios para evitar chamadas HTTP reais nos testes
jest.mock('axios', () => ({
    post: jest.fn().mockResolvedValue({ status: 200 })
}));

describe('Telegram Notifications - Testes Reais', () => {
    let telegramService: TelegramService;
    let notificationService: NotificationService;
    
    // Flag para determinar se deve enviar mensagens reais ou usar mocks
    const SEND_REAL_MESSAGES = process.env.TEST_REAL_TELEGRAM === 'true';
    
    beforeAll(() => {
        if (SEND_REAL_MESSAGES) {
            console.log('🚨 ATENÇÃO: Os testes irão enviar mensagens REAIS para o Telegram!');
            console.log('Configure TELEGRAM_BOT_TOKEN e TELEGRAM_CHAT_ID no .env');
        }
        
        telegramService = TelegramService.getInstance();
        notificationService = new NotificationService();
    });

    beforeEach(() => {
        if (!SEND_REAL_MESSAGES) {
            // Mock do método sendMessage quando não está enviando mensagens reais
            jest.spyOn(telegramService as any, 'sendMessage').mockResolvedValue(undefined);
        }
    });

    afterEach(() => {
        if (!SEND_REAL_MESSAGES) {
            jest.restoreAllMocks();
        }
    });

    describe('Cenários de Trade Válido', () => {
        test('1. Trade válido LONG com execução bem-sucedida', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'BTCUSDT',
                type: 'LONG',
                entry: 45000,
                stop: 44000,
                takeProfits: {
                    tp1: 46000,
                    tp2: 47000,
                    tp3: 48000,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: true,
                    message: 'Trade is valid: Entry conditions met and volume is high (yellow)',
                    volumeAnalysis: {
                        color: 'yellow',
                        stdBar: 2.5,
                        currentVolume: 1000000,
                        mean: 800000,
                        std: 200000
                    },
                    entryAnalysis: {
                        currentClose: 45100,
                        canEnter: true,
                        hasClosePriceBeforeEntry: true,
                        message: 'Entry condition met and price has been close before entry'
                    }
                },
                analysisUrl: 'https://tradingview.com/chart/BTCUSDT',
                executionResult: {
                    leverage: 10,
                    quantity: 0.1,
                    entryOrderId: 'TEST_ENTRY_123',
                    stopOrderId: 'TEST_STOP_123',
                    volumeMarginAdded: {
                        percentage: 15,
                        baseMargin: 450,
                        totalMargin: 517.5
                    }
                },
                volume_required: true,
                volume_adds_margin: true,
                setup_description: 'Breakout de resistência com volume alto',
                isWarning: false,
                manually_generated: false,
                interval: '1h'
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                // Aguarda um pouco para evitar spam
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });

        test('2. Trade válido SHORT com execução bem-sucedida', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'ETHUSDT',
                type: 'SHORT',
                entry: 2500,
                stop: 2600,
                takeProfits: {
                    tp1: 2400,
                    tp2: 2300,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: true,
                    message: 'Trade is valid: Entry conditions met and volume is high (red)',
                    volumeAnalysis: {
                        color: 'red',
                        stdBar: 3.2,
                        currentVolume: 2000000,
                        mean: 1200000,
                        std: 300000
                    },
                    entryAnalysis: {
                        currentClose: 2480,
                        canEnter: true,
                        hasClosePriceBeforeEntry: true,
                        message: 'Entry condition met and price has been close before entry'
                    }
                },
                analysisUrl: 'https://tradingview.com/chart/ETHUSDT',
                executionResult: {
                    leverage: 15,
                    quantity: 0.5,
                    entryOrderId: 'TEST_ENTRY_456',
                    stopOrderId: 'TEST_STOP_456'
                },
                volume_required: true,
                volume_adds_margin: false,
                setup_description: 'Rompimento de suporte com confirmação',
                isWarning: false,
                manually_generated: true,
                interval: '4h'
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });
    });

    describe('Cenários de Trade com Aviso (Warning)', () => {
        test('3. Trade com aviso - Condições parcialmente atendidas', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'ADAUSDT',
                type: 'LONG',
                entry: 0.45,
                stop: 0.42,
                takeProfits: {
                    tp1: 0.48,
                    tp2: 0.50,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: false,
                    message: 'Trade is invalid: Entry condition not met. Current close (0.44) is not above entry (0.45)',
                    volumeAnalysis: {
                        color: 'yellow',
                        stdBar: 1.8,
                        currentVolume: 500000,
                        mean: 400000,
                        std: 100000
                    },
                    entryAnalysis: {
                        currentClose: 0.44,
                        canEnter: false,
                        hasClosePriceBeforeEntry: true,
                        message: 'Entry condition not met. Current close (0.44) is not above entry (0.45)'
                    }
                },
                analysisUrl: 'https://tradingview.com/chart/ADAUSDT',
                volume_required: true,
                volume_adds_margin: false,
                setup_description: 'Aguardando rompimento da resistência',
                isWarning: true,
                manually_generated: false,
                interval: '1h'
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });

        test('4. Trade com aviso - Volume baixo', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'DOTUSDT',
                type: 'SHORT',
                entry: 6.50,
                stop: 6.80,
                takeProfits: {
                    tp1: 6.20,
                    tp2: null,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: false,
                    message: 'Trade is invalid: Volume is not high enough (blue)',
                    volumeAnalysis: {
                        color: 'blue',
                        stdBar: -0.5,
                        currentVolume: 200000,
                        mean: 300000,
                        std: 50000
                    },
                    entryAnalysis: {
                        currentClose: 6.45,
                        canEnter: true,
                        hasClosePriceBeforeEntry: true,
                        message: 'Entry condition met and price has been close before entry'
                    }
                },
                analysisUrl: 'https://tradingview.com/chart/DOTUSDT',
                volume_required: true,
                volume_adds_margin: false,
                setup_description: 'Volume insuficiente para entrada',
                isWarning: true,
                manually_generated: false,
                interval: '15m'
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });
    });

    describe('Cenários de Erro de Execução', () => {
        test('5. Trade válido - Credenciais API não configuradas', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'BNBUSDT',
                type: 'LONG',
                entry: 300,
                stop: 290,
                takeProfits: {
                    tp1: 310,
                    tp2: 320,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: true,
                    message: 'Trade forced by user but skipped: Missing BingX API credentials',
                    volumeAnalysis: {
                        color: 'yellow',
                        stdBar: 2.1,
                        currentVolume: 800000,
                        mean: 600000,
                        std: 150000
                    },
                    entryAnalysis: {
                        currentClose: 305,
                        canEnter: true,
                        hasClosePriceBeforeEntry: true,
                        message: 'Entry condition met and price has been close before entry'
                    }
                },
                analysisUrl: 'https://tradingview.com/chart/BNBUSDT',
                executionError: 'BingX API credentials not configured',
                volume_required: false,
                volume_adds_margin: true,
                setup_description: 'Trade manual forçado',
                isWarning: false,
                manually_generated: true,
                interval: '1h'
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });

        test('6. Trade válido - Falha na execução por saldo insuficiente', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'SOLUSDT',
                type: 'SHORT',
                entry: 100,
                stop: 105,
                takeProfits: {
                    tp1: 95,
                    tp2: 90,
                    tp3: 85,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: true,
                    message: 'Trade forced by user',
                    volumeAnalysis: {
                        color: 'red',
                        stdBar: 4.2,
                        currentVolume: 1500000,
                        mean: 900000,
                        std: 200000
                    },
                    entryAnalysis: {
                        currentClose: 98,
                        canEnter: true,
                        hasClosePriceBeforeEntry: true,
                        message: 'Entry condition met and price has been close before entry'
                    }
                },
                analysisUrl: 'https://tradingview.com/chart/SOLUSDT',
                executionError: 'Insufficient balance for trade execution',
                volume_required: true,
                volume_adds_margin: false,
                setup_description: 'Breakdown com volume alto',
                isWarning: false,
                manually_generated: true,
                interval: '4h'
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });

        test('7. Erro ao cancelar ordem órfã', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'LINKUSDT',
                type: 'LONG',
                entry: 0,
                stop: 15.5,
                takeProfits: {
                    tp1: 0,
                    tp2: null,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: false,
                    message: 'Failed to cancel orphaned STOP_MARKET order for LINKUSDT LONG',
                    volumeAnalysis: {
                        color: 'red',
                        stdBar: 0,
                        currentVolume: 0,
                        mean: 0,
                        std: 0
                    },
                    entryAnalysis: {
                        currentClose: 0,
                        canEnter: false,
                        hasClosePriceBeforeEntry: true,
                        message: 'Error cancelling orphaned order'
                    }
                },
                analysisUrl: '',
                volume_required: false,
                volume_adds_margin: false,
                setup_description: '❌ Failed to cancel orphaned STOP_MARKET order for LINKUSDT LONG. Order ID: TEST_ORDER_789, Error: Order not found',
                interval: null
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });
    });

    describe('Mensagens Customizadas', () => {
        test('8. Mensagem customizada - Status do sistema', async () => {
            const customMessage = `
🤖 <b>Sistema de Trading - Status</b>

✅ <b>Serviços Ativos:</b>
• Monitor de Posições: Online
• Analisador de Volume: Online  
• Webhook Receiver: Online
• Database: Conectado

📊 <b>Estatísticas do Dia:</b>
• Trades Processados: 15
• Trades Executados: 8
• Taxa de Sucesso: 73%
• PnL Realizado: +$247.50

⏰ Última atualização: ${new Date().toLocaleString()}

🔔 Todos os sistemas funcionando normalmente!
            `.trim();

            await expect(telegramService.sendCustomMessage(customMessage))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });

        test('9. Mensagem customizada - Alerta de sistema', async () => {
            const alertMessage = `
⚠️ <b>ALERTA DO SISTEMA</b>

🚨 <b>Problema Detectado:</b>
• WebSocket BingX desconectado
• Tentativas de reconexão: 3/5

🔧 <b>Ações Tomadas:</b>
• Pausando execução de novos trades
• Mantendo monitoramento de posições ativas
• Tentando reconexão automática

⏰ Ocorrido em: ${new Date().toLocaleString()}

🔔 Sistema tentará reconectar automaticamente.
Se o problema persistir, verificar conexão de rede.
            `.trim();

            await expect(telegramService.sendCustomMessage(alertMessage))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });
    });

    describe('Cenários Edge Cases', () => {
        test('10. Trade com todos os Take Profits preenchidos', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'AVAXUSDT',
                type: 'LONG',
                entry: 25,
                stop: 23,
                takeProfits: {
                    tp1: 26,
                    tp2: 27,
                    tp3: 28,
                    tp4: 29,
                    tp5: 30,
                    tp6: 31
                },
                validation: {
                    isValid: true,
                    message: 'Trade is valid: Entry conditions met and volume is optional (green)',
                    volumeAnalysis: {
                        color: 'green',
                        stdBar: 1.5,
                        currentVolume: 300000,
                        mean: 250000,
                        std: 75000
                    },
                    entryAnalysis: {
                        currentClose: 25.5,
                        canEnter: true,
                        hasClosePriceBeforeEntry: true,
                        message: 'Entry condition met and price has been close before entry'
                    }
                },
                analysisUrl: 'https://tradingview.com/chart/AVAXUSDT',
                executionResult: {
                    leverage: 8,
                    quantity: 2.0,
                    entryOrderId: 'TEST_ENTRY_FULL',
                    stopOrderId: 'TEST_STOP_FULL',
                    volumeMarginAdded: {
                        percentage: 0,
                        baseMargin: 625,
                        totalMargin: 625
                    }
                },
                volume_required: false,
                volume_adds_margin: false,
                setup_description: 'Setup completo com 6 TPs - Strategy scalping avançada',
                isWarning: false,
                manually_generated: false,
                interval: '5m'
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });

        test('11. Trade com TP1 modificado', async () => {
            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'MATICUSDT',
                type: 'SHORT',
                entry: 0.8,
                stop: 0.85,
                takeProfits: {
                    tp1: 0.75, // TP1 foi ajustado
                    tp2: 0.7,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: true,
                    message: 'Trade forced by user (TP1 adjusted)',
                    volumeAnalysis: {
                        color: 'yellow',
                        stdBar: 2.8,
                        currentVolume: 1200000,
                        mean: 800000,
                        std: 180000
                    },
                    entryAnalysis: {
                        currentClose: 0.78,
                        canEnter: true,
                        hasClosePriceBeforeEntry: true,
                        message: 'Entry condition met and price has been close before entry'
                    }
                },
                analysisUrl: 'https://tradingview.com/chart/MATICUSDT',
                executionResult: {
                    leverage: 12,
                    quantity: 500,
                    entryOrderId: 'TEST_ENTRY_MODIFIED',
                    stopOrderId: 'TEST_STOP_MODIFIED'
                },
                volume_required: true,
                volume_adds_margin: true,
                setup_description: 'Trade manual com TP1 ajustado para melhor R:R',
                isWarning: false,
                manually_generated: true,
                interval: '1h'
            };

            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            if (SEND_REAL_MESSAGES) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        });
    });

    describe('Configuração do Telegram', () => {
        test('12. Verificar se o serviço está configurado corretamente', () => {
            if (SEND_REAL_MESSAGES) {
                expect(telegramService.isConfigured()).toBe(true);
                expect(process.env.TELEGRAM_BOT_TOKEN).toBeDefined();
                expect(process.env.TELEGRAM_CHAT_ID).toBeDefined();
            } else {
                // Em modo mock, pode ou não estar configurado
                const isConfigured = telegramService.isConfigured();
                console.log(`Telegram configurado: ${isConfigured}`);
                expect(typeof isConfigured).toBe('boolean');
            }
        });

        test('13. Testar comportamento quando não configurado', async () => {
            // Temporariamente mock o método isConfigured para retornar false
            const originalIsConfigured = telegramService.isConfigured;
            (telegramService as any).isConfigured = jest.fn().mockReturnValue(false);

            const notification: Omit<TradeNotification, 'timestamp'> = {
                symbol: 'TESTUSDT',
                type: 'LONG',
                entry: 1,
                stop: 0.9,
                takeProfits: {
                    tp1: 1.1,
                    tp2: null,
                    tp3: null,
                    tp4: null,
                    tp5: null,
                    tp6: null
                },
                validation: {
                    isValid: true,
                    message: 'Test message',
                    volumeAnalysis: {
                        color: 'yellow',
                        stdBar: 1,
                        currentVolume: 100,
                        mean: 100,
                        std: 10
                    },
                    entryAnalysis: {
                        currentClose: 1.01,
                        canEnter: true,
                        hasClosePriceBeforeEntry: true,
                        message: 'Test entry'
                    }
                },
                analysisUrl: '',
                volume_required: false,
                volume_adds_margin: false,
                setup_description: 'Test trade when not configured',
                isWarning: false
            };

            // Não deve lançar erro, deve apenas pular o envio
            await expect(notificationService.sendTradeNotification(notification))
                .resolves.not.toThrow();

            // Restaura o método original
            (telegramService as any).isConfigured = originalIsConfigured;
        });
    });

    afterAll(async () => {
        if (SEND_REAL_MESSAGES) {
            // Envia mensagem final indicando que os testes terminaram
            const finalMessage = `
🧪 <b>Testes de Notificação Concluídos</b>

✅ <b>Todos os cenários testados com sucesso!</b>

📋 <b>Cenários Testados:</b>
• Trade válido LONG e SHORT
• Trades com avisos (warnings)
• Erros de execução
• Mensagens customizadas
• Cenários edge cases
• Configuração do sistema

⏰ Finalizado em: ${new Date().toLocaleString()}

🎯 Sistema de notificações funcionando perfeitamente!
            `.trim();

            await telegramService.sendCustomMessage(finalMessage);
        }
    });
}); 